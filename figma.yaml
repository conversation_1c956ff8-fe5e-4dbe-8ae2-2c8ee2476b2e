{document: {id: "500:4293", name: Today Page, type: FRAME, boundVariables: {paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:19:7}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:19:7}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}]}, explicitVariableModes: {VariableCollectionId:1:35: "2:2"}, children: [{id: "500:4294", name: Today page Container, type: FRAME, children: [{id: "500:4295", name: Today page Container, type: FRAME, children: [{id: "500:4296", name: Container 1, type: FRAME, boundVariables: {itemSpacing: {type: VA<PERSON>ABLE_ALIAS, id: VariableID:2:49}}, children: [{id: "500:4297", name: Page Title/Default, type: INSTANCE, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}]}, componentId: "153:587", overrides: [], children: [{id: I500:4297;153:543, name: Icon, type: INSTANCE, componentId: "153:88", componentProperties: {Icon: {value: Menu, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4297;153:543;153:87, name: Menu, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 868, "y": -5651.26904296875, width: 16.00001335144043, height: 10.539000511169434}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 864, "y": -5658, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4297;153:545, name: Index, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:46}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 888, "y": -5656.5, width: 313, height: 21}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Index, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 16, textAlignHorizontal: CENTER, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 20.799999237060547, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4297;153:561, name: Icon, type: INSTANCE, componentId: "153:89", componentProperties: {Icon: {value: Back, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4297;153:561;153:96, name: Back, type: INSTANCE, rotation: -3.141592653589793, componentId: "153:95", overrides: [], children: [{id: I500:4297;153:561;153:96;153:94, name: Vector, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1208.999981880188, "y": -5654.5, width: 8.998675346374512, height: 17}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1207, "y": -5658, width: 12, height: 24}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1201, "y": -5658, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: true, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 49, layoutWrap: WRAP, counterAxisSpacing: 49, counterAxisAlignContent: AUTO, absoluteBoundingBox: {x: 864, "y": -5658, width: 361, height: 24}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4298", name: Container 2, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:49}}, children: [{id: "500:4299", name: To do list Container, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}}, children: [{id: "500:4300", name: To do list Container, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}]}, children: [{id: "500:4301", name: Title, type: INSTANCE, componentId: "216:1628", componentProperties: {Property 1: {value: Variant1, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4301", overriddenFields: [clipsContent, height, paddingLeft, paddingRight, width]}, {id: I500:4301;216:1629, overriddenFields: [boundVariables, characterStyleOverrides, characters, lineIndentations, lineTypes, styleOverrideTable]}, {id: I500:4301;216:1630, overriddenFields: [fills]}], children: [{id: I500:4301;216:1629, name: To do list, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:45}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 864, "y": -5611, width: 337, height: 18}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: To do List, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 14, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 18.19999885559082, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4301;216:1630, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, componentId: "153:180", componentProperties: {Icon: {value: Plus, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4301;216:1630, overriddenFields: [fills]}], children: [{id: I500:4301;216:1630;153:179, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1208, "y": -5607, width: 10, height: 10}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, absoluteBoundingBox: {x: 1201, "y": -5614, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: true, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 134, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5614, width: 361, height: 24}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4302", name: Mobile App & UI/UX Container, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}}, children: [{id: "500:4303", name: Cards, type: INSTANCE, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:47}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}], strokes: [{type: VARIABLE_ALIAS, id: VariableID:23:184}]}, componentId: "466:5282", componentProperties: {Cards: {value: Card Container 1, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4303", overriddenFields: [height, width]}, {id: I500:4303;466:5291, overriddenFields: [explicitVariableModes, fills]}, {id: I500:4303;466:5291;466:5253, overriddenFields: [fills]}, {id: I500:4303;466:5291;466:5255, overriddenFields: [fills]}], children: [{id: I500:4303;466:5283, name: Container 1, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4303;466:5284, name: "Title, description & Icon", type: FRAME, children: [{id: I500:4303;466:5285, name: Title & description, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4303;466:5286, name: Mobile App Design, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:44}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5562, width: 327, height: 16}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Mobile App Design, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 12, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 15.59999942779541, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4303;466:5287, name: "Wireframing, Colors, Fonts", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:43}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5542, width: 327, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: "Wireframing, Colors, Fonts ", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5562, width: 327, height: 33}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4303;466:5288, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}}, componentId: "466:5047", componentProperties: {Icon: {value: Dot 2, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4303;466:5288;466:5048, name: qlementine-icons:menu-dots-16, type: INSTANCE, componentId: "153:548", overrides: [], children: [{id: I500:4303;466:5288;466:5048;153:547, name: Vector, visible: false, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1206.875, "y": -5558.625, width: 2.25, height: 11.24887466430664}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5562, width: 18, height: 18}}, {id: I500:4303;466:5288;466:5049, name: Vector, type: VECTOR, rotation: -1.5707963267948968, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1202.375, "y": -5554.125, width: 11.24887466430664, height: 2.25}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5562, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 149, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5562, width: 345, height: 33}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4303;466:5289, name: Progress bar and percentage, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4303;466:5290, name: Progress 30%, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5521, width: 345, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Progress 30%, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: RIGHT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4303;466:5291, name: Progress Bar, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2048}]}, explicitVariableModes: {VariableCollectionId:1:35: "2:2"}, componentId: "466:5251", componentProperties: {Progress Bar: {value: 30%, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4303;466:5291, overriddenFields: [explicitVariableModes, fills]}, {id: I500:4303;466:5291;466:5253, overriddenFields: [fills]}, {id: I500:4303;466:5291;466:5255, overriddenFields: [fills]}], children: [{id: I500:4303;466:5291;466:5252, name: Rectangle 2, type: GROUP, children: [{id: I500:4303;466:5291;466:5253, name: Rectangle 2, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2048}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.4431372582912445, g: 0.4431372582912445, b: 0.4431372582912445, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2048}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5500, width: 345, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5500, width: 345, height: 8}}, {id: I500:4303;466:5291;466:5254, name: Rectangle 1, type: GROUP, children: [{id: I500:4303;466:5291;466:5255, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:216:1259}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.7764706015586853, g: 0.7764706015586853, b: 0.7764706015586853, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:216:1259}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5500, width: 83.94999694824219, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5500, width: 83.94999694824219, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.4431372582912445, g: 0.4431372582912445, b: 0.4431372582912445, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2048}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.4431372582912445, g: 0.4431372582912445, b: 0.4431372582912445, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2048}}}], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.4431372582912445, g: 0.4431372582912445, b: 0.4431372582912445, a: 1}, absoluteBoundingBox: {x: 872, "y": -5500, width: 345, height: 8}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: MAX, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5521, width: 345, height: 29}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5562, width: 345, height: 70}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4303;466:5292, name: Container 2, type: FRAME, children: [{id: I500:4303;466:5293, name: Date, type: FRAME, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, children: [{id: I500:4303;466:5294, name: 22 March 2025, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 882, "y": -5470, width: 72, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: 22 March 2025, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 12, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, layoutMode: HORIZONTAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, paddingLeft: 10, paddingRight: 10, paddingTop: 10, paddingBottom: 10, itemSpacing: 10, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5476, width: 92, height: 25}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: FIXED}, {id: I500:4303;466:5295, name: Icon, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4303;466:5296, name: Comment, type: FRAME, children: [{id: I500:4303;466:5297, name: Icon, type: INSTANCE, componentId: "466:5060", componentProperties: {Icon: {value: Comment, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4303;466:5297;466:5061, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1159.25, "y": -5470.25, width: 13.500003814697266, height: 13.50000286102295}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1157, "y": -5472.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4303;466:5298, name: "3", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1177, "y": -5470, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "3", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5472.5, width: 29, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4303;466:5299, name: Copy link, type: FRAME, children: [{id: I500:4303;466:5300, name: Icon, type: INSTANCE, componentId: "466:5058", componentProperties: {Icon: {value: Copy link, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4303;466:5300;466:5059, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1192.249755859375, "y": -5470.25, width: 13.500127792358398, height: 13.499916076660156}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1190, "y": -5472.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4303;466:5301, name: "5", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1210, "y": -5470, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "5", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1190, "y": -5472.5, width: 27, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5472.5, width: 60, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 148, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5476, width: 345, height: 25}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:23:184}}}], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, paddingLeft: 8, paddingRight: 8, paddingTop: 12, paddingBottom: 12, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5574, width: 361, height: 135}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4304", name: Cards, type: INSTANCE, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:47}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}], strokes: [{type: VARIABLE_ALIAS, id: VariableID:23:184}]}, componentId: "466:5302", componentProperties: {Cards: {value: Card Container 2, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4304", overriddenFields: [height, width]}, {id: I500:4304;466:5311;466:5258, overriddenFields: [fills]}, {id: I500:4304;466:5311;466:5260, overriddenFields: [fills]}], children: [{id: I500:4304;466:5303, name: Containter 1, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4304;466:5304, name: "Title, Description & icon", type: FRAME, children: [{id: I500:4304;466:5305, name: Title & description, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4304;466:5306, name: Web App Design, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:44}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5411, width: 327, height: 16}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Web App Design, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 12, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 15.59999942779541, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4304;466:5307, name: "Wireframing, Colors, Fonts", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5391, width: 124, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG, characters: "Wireframing, Colors, Fonts ", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5411, width: 327, height: 33}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4304;466:5308, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}}, componentId: "466:5047", componentProperties: {Icon: {value: Dot 2, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4304;466:5308;466:5048, name: qlementine-icons:menu-dots-16, visible: false, type: INSTANCE, componentId: "153:548", overrides: [], children: [{id: I500:4304;466:5308;466:5048;153:547, name: Vector, visible: false, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1206.875, "y": -5407.625, width: 2.25, height: 11.24887466430664}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5411, width: 18, height: 18}}, {id: I500:4304;466:5308;466:5049, name: Dot menu, type: VECTOR, rotation: -1.5707963267948968, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1202.375, "y": -5403.125, width: 11.24887466430664, height: 2.25}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5411, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 149, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5411, width: 345, height: 33}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4304;466:5309, name: Progree bar & percentage, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4304;466:5310, name: Progress 50%, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5370, width: 345, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Progress 50%, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: RIGHT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4304;466:5311, name: Progress Bar, type: INSTANCE, componentId: "466:5256", componentProperties: {Progress Bar: {value: 50%, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4304;466:5311;466:5258, overriddenFields: [fills]}, {id: I500:4304;466:5311;466:5260, overriddenFields: [fills]}], children: [{id: I500:4304;466:5311;466:5257, name: Rectangle 2, type: GROUP, children: [{id: I500:4304;466:5311;466:5258, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2048}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.4431372582912445, g: 0.4431372582912445, b: 0.4431372582912445, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2048}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5349, width: 345, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5349, width: 345, height: 8}}, {id: I500:4304;466:5311;466:5259, name: Rectangle 1, type: GROUP, children: [{id: I500:4304;466:5311;466:5260, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:216:1259}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.7764706015586853, g: 0.7764706015586853, b: 0.7764706015586853, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:216:1259}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5349, width: 132.25, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5349, width: 132.25, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5349, width: 345, height: 8}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: MAX, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5370, width: 345, height: 29}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5411, width: 345, height: 70}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4304;466:5312, name: Container 2, type: FRAME, children: [{id: I500:4304;466:5313, name: Date, type: FRAME, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, children: [{id: I500:4304;466:5314, name: 22 March 2025, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 882, "y": -5319, width: 72, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: 22 March 2025, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 12, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, layoutMode: HORIZONTAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, paddingLeft: 10, paddingRight: 10, paddingTop: 10, paddingBottom: 10, itemSpacing: 10, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5325, width: 92, height: 25}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: FIXED}, {id: I500:4304;466:5315, name: Icon, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4304;466:5316, name: Comment, type: FRAME, children: [{id: I500:4304;466:5317, name: Icon, type: INSTANCE, componentId: "466:5060", componentProperties: {Icon: {value: Comment, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4304;466:5317;466:5061, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1159.25, "y": -5319.25, width: 13.500003814697266, height: 13.50000286102295}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1157, "y": -5321.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4304;466:5318, name: "3", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1177, "y": -5319, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "3", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5321.5, width: 29, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4304;466:5319, name: Copy link, type: FRAME, children: [{id: I500:4304;466:5320, name: Icon, type: INSTANCE, componentId: "466:5058", componentProperties: {Icon: {value: Copy link, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4304;466:5320;466:5059, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1192.249755859375, "y": -5319.25, width: 13.500127792358398, height: 13.499916076660156}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1190, "y": -5321.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4304;466:5321, name: "5", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1210, "y": -5319, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "5", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1190, "y": -5321.5, width: 27, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5321.5, width: 60, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 148, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5325, width: 345, height: 25}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:23:184}}}], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, paddingLeft: 8, paddingRight: 8, paddingTop: 12, paddingBottom: 12, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5423, width: 361, height: 135}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5574, width: 361, height: 286}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5614, width: 361, height: 326}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5614, width: 361, height: 326}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4305", name: In Progress Container, type: FRAME, children: [{id: "500:4306", name: In Progress Container, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:44}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:44}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}]}, children: [{id: "500:4307", name: Title, type: INSTANCE, componentId: "216:1628", componentProperties: {Property 1: {value: Variant1, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4307", overriddenFields: [clipsContent, height, paddingLeft, paddingRight, width]}, {id: I500:4307;216:1629, overriddenFields: [boundVariables, characterStyleOverrides, characters, fills, lineIndentations, lineTypes, strokes, styleOverrideTable]}, {id: I500:4307;216:1630, overriddenFields: [fills]}, {id: I500:4307;216:1630;153:179, overriddenFields: [strokes]}], children: [{id: I500:4307;216:1629, name: To do list, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:45}], textRangeFills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 864, "y": -5265, width: 337, height: 18}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: "In Progress    ", characterStyleOverrides: [14, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13], styleOverrideTable: {"13": {fontFamily: Geist, fontPostScriptName: Geist-SemiBold, fontStyle: SemiBold, fontWeight: 600, fontSize: 14, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], boundVariables: {fontWeight: {type: VARIABLE_ALIAS, id: VariableID:20:18}, fontSize: {type: VARIABLE_ALIAS, id: VariableID:20:45}}}, "14": {fontFamily: Geist, fontPostScriptName: Geist-SemiBold, fontStyle: SemiBold, fontWeight: 600, fontSize: 14, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], boundVariables: {fontWeight: {type: VARIABLE_ALIAS, id: VariableID:20:18}, fontSize: {type: VARIABLE_ALIAS, id: VariableID:20:45}}}}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: "", fontPostScriptName: null, fontStyle: "", fontWeight: 500, textAutoResize: HEIGHT, fontSize: 14, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 16.40625, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4307;216:1630, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, componentId: "153:180", componentProperties: {Icon: {value: Plus, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4307;216:1630, overriddenFields: [fills]}, {id: I500:4307;216:1630;153:179, overriddenFields: [strokes]}], children: [{id: I500:4307;216:1630;153:179, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1208, "y": -5261, width: 10, height: 10}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, absoluteBoundingBox: {x: 1201, "y": -5268, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: true, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 134, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5268, width: 361, height: 24}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4308", name: Cards, type: INSTANCE, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:47}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, strokes: [{type: VARIABLE_ALIAS, id: VariableID:23:184}]}, componentId: "466:5302", componentProperties: {Cards: {value: Card Container 2, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4308", overriddenFields: [fills, height, width]}, {id: I500:4308;466:5306, overriddenFields: [characterStyleOverrides, characters, lineIndentations, lineTypes, name, styleOverrideTable]}, {id: I500:4308;466:5311;466:5258, overriddenFields: [fills]}, {id: I500:4308;466:5311;466:5260, overriddenFields: [fills]}], children: [{id: I500:4308;466:5303, name: Containter 1, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4308;466:5304, name: "Title, Description & icon", type: FRAME, children: [{id: I500:4308;466:5305, name: Title & description, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4308;466:5306, name: UI/Ux Design, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:44}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5216, width: 327, height: 16}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: UI/UX Design, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 12, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 15.59999942779541, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4308;466:5307, name: "Wireframing, Colors, Fonts", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5196, width: 124, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG, characters: "Wireframing, Colors, Fonts ", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5216, width: 327, height: 33}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4308;466:5308, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}}, componentId: "466:5047", componentProperties: {Icon: {value: Dot 2, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4308;466:5308;466:5048, name: qlementine-icons:menu-dots-16, visible: false, type: INSTANCE, componentId: "153:548", overrides: [], children: [{id: I500:4308;466:5308;466:5048;153:547, name: Vector, visible: false, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1206.875, "y": -5212.625, width: 2.25, height: 11.24887466430664}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5216, width: 18, height: 18}}, {id: I500:4308;466:5308;466:5049, name: Dot menu, type: VECTOR, rotation: -1.5707963267948968, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1202.375, "y": -5208.125, width: 11.24887466430664, height: 2.25}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5216, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 149, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5216, width: 345, height: 33}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4308;466:5309, name: Progree bar & percentage, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4308;466:5310, name: Progress 50%, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5175, width: 345, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Progress 50%, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: RIGHT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4308;466:5311, name: Progress Bar, type: INSTANCE, componentId: "466:5256", componentProperties: {Progress Bar: {value: 50%, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4308;466:5311;466:5258, overriddenFields: [fills]}, {id: I500:4308;466:5311;466:5260, overriddenFields: [fills]}], children: [{id: I500:4308;466:5311;466:5257, name: Rectangle 2, type: GROUP, children: [{id: I500:4308;466:5311;466:5258, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:69:675}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.886274516582489, g: 0.8627451062202454, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:69:675}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5154, width: 345, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5154, width: 345, height: 8}}, {id: I500:4308;466:5311;466:5259, name: Rectangle 1, type: GROUP, children: [{id: I500:4308;466:5311;466:5260, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:18:3}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.5398693084716797, g: 0.4492374658584595, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:18:3}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5154, width: 132.25, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5154, width: 132.25, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5154, width: 345, height: 8}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: MAX, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5175, width: 345, height: 29}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5216, width: 345, height: 70}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4308;466:5312, name: Container 2, type: FRAME, children: [{id: I500:4308;466:5313, name: Date, type: FRAME, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, children: [{id: I500:4308;466:5314, name: 22 March 2025, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 882, "y": -5124, width: 72, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: 22 March 2025, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 12, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, layoutMode: HORIZONTAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, paddingLeft: 10, paddingRight: 10, paddingTop: 10, paddingBottom: 10, itemSpacing: 10, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5130, width: 92, height: 25}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: FIXED}, {id: I500:4308;466:5315, name: Icon, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4308;466:5316, name: Comment, type: FRAME, children: [{id: I500:4308;466:5317, name: Icon, type: INSTANCE, componentId: "466:5060", componentProperties: {Icon: {value: Comment, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4308;466:5317;466:5061, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1159.25, "y": -5124.25, width: 13.500003814697266, height: 13.50000286102295}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1157, "y": -5126.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4308;466:5318, name: "3", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1177, "y": -5124, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "3", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5126.5, width: 29, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4308;466:5319, name: Copy link, type: FRAME, children: [{id: I500:4308;466:5320, name: Icon, type: INSTANCE, componentId: "466:5058", componentProperties: {Icon: {value: Copy link, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4308;466:5320;466:5059, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1192.249755859375, "y": -5124.25, width: 13.500127792358398, height: 13.499916076660156}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1190, "y": -5126.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4308;466:5321, name: "5", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1210, "y": -5124, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "5", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1190, "y": -5126.5, width: 27, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -5126.5, width: 60, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 148, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5130, width: 345, height: 25}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:23:184}}}], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, paddingLeft: 8, paddingRight: 8, paddingTop: 12, paddingBottom: 12, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5228, width: 361, height: 135}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: "500:4309", name: Cards, type: INSTANCE, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:48}, paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:46}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:47}, rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, strokes: [{type: VARIABLE_ALIAS, id: VariableID:23:184}]}, componentId: "466:5302", componentProperties: {Cards: {value: Card Container 2, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4309", overriddenFields: [fills, height, width]}, {id: I500:4309;466:5306, overriddenFields: [characterStyleOverrides, characters, lineIndentations, lineTypes, locked, name, styleOverrideTable]}, {id: I500:4309;466:5310, overriddenFields: [characterStyleOverrides, characters, lineIndentations, lineTypes, name, styleOverrideTable]}, {id: I500:4309;466:5311;466:5265, overriddenFields: [fills]}], children: [{id: I500:4309;466:5303, name: Containter 1, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4309;466:5304, name: "Title, Description & icon", type: FRAME, children: [{id: I500:4309;466:5305, name: Title & description, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4309;466:5306, name: Website Design, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:44}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5065, width: 327, height: 16}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Website Design, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: HEIGHT, fontSize: 12, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 15.59999942779541, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4309;466:5307, name: "Wireframing, Colors, Fonts", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5045, width: 124, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG, characters: "Wireframing, Colors, Fonts ", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5065, width: 327, height: 33}, layoutAlign: INHERIT, layoutGrow: 1, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4309;466:5308, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}}, componentId: "466:5047", componentProperties: {Icon: {value: Dot 2, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4309;466:5308;466:5048, name: qlementine-icons:menu-dots-16, visible: false, type: INSTANCE, componentId: "153:548", overrides: [], children: [{id: I500:4309;466:5308;466:5048;153:547, name: Vector, visible: false, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1206.875, "y": -5061.625, width: 2.25, height: 11.24887466430664}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5065, width: 18, height: 18}}, {id: I500:4309;466:5308;466:5049, name: Dot menu, type: VECTOR, rotation: -1.5707963267948968, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1202.375, "y": -5057.125, width: 11.24887466430664, height: 2.25}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1199, "y": -5065, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 149, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5065, width: 345, height: 33}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4309;466:5309, name: Progree bar & percentage, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:46}}, children: [{id: I500:4309;466:5310, name: Progress 70%, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 872, "y": -5024, width: 345, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Progress 70%, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: RIGHT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}, {id: I500:4309;466:5311, name: Progress Bar, type: INSTANCE, componentId: "466:5261", componentProperties: {Progress Bar: {value: 70%, type: VARIANT, boundVariables: {}}}, overrides: [{id: I500:4309;466:5311;466:5265, overriddenFields: [fills]}], children: [{id: I500:4309;466:5311;466:5262, name: Rectangle 2, type: GROUP, children: [{id: I500:4309;466:5311;466:5263, name: Rectangle 2, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:69:675}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.886274516582489, g: 0.8627451062202454, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:69:675}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5003, width: 345, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5003, width: 345, height: 8}}, {id: I500:4309;466:5311;466:5264, name: Rectangle 1, type: GROUP, children: [{id: I500:4309;466:5311;466:5265, name: Rectangle 1, type: RECTANGLE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:38:12}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:18:3}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.5398693084716797, g: 0.4492374658584595, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:18:3}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, cornerRadius: 8, cornerSmoothing: 0, absoluteBoundingBox: {x: 872, "y": -5003, width: 221.9499969482422, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5003, width: 221.9499969482422, height: 8}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 872, "y": -5003, width: 345, height: 8}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: MAX, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5024, width: 345, height: 29}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 8, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -5065, width: 345, height: 70}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}, {id: I500:4309;466:5312, name: Container 2, type: FRAME, children: [{id: I500:4309;466:5313, name: Date, type: FRAME, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:71}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:223:2182}]}, children: [{id: I500:4309;466:5314, name: 22 March 2025, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 882, "y": -4973, width: 72, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: 22 March 2025, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:223:2182}}}], strokes: [], cornerRadius: 12, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, layoutMode: HORIZONTAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, paddingLeft: 10, paddingRight: 10, paddingTop: 10, paddingBottom: 10, itemSpacing: 10, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -4979, width: 92, height: 25}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: FIXED}, {id: I500:4309;466:5315, name: Icon, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:45}}, children: [{id: I500:4309;466:5316, name: Comment, type: FRAME, children: [{id: I500:4309;466:5317, name: Icon, type: INSTANCE, componentId: "466:5060", componentProperties: {Icon: {value: Comment, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4309;466:5317;466:5061, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1159.25, "y": -4973.25, width: 13.500003814697266, height: 13.50000286102295}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1157, "y": -4975.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4309;466:5318, name: "3", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1177, "y": -4973, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "3", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -4975.5, width: 29, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4309;466:5319, name: Copy link, type: FRAME, children: [{id: I500:4309;466:5320, name: Icon, type: INSTANCE, componentId: "466:5058", componentProperties: {Icon: {value: Copy link, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4309;466:5320;466:5059, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:116:453}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1192.249755859375, "y": -4973.25, width: 13.500127792358398, height: 13.499916076660156}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1190, "y": -4975.5, width: 18, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4309;466:5321, name: "5", type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:43}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:20:18}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6666666865348816, g: 0.6666666865348816, b: 0.6666666865348816, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:43}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1210, "y": -4973, width: 7, height: 13}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG, characters: "5", characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Medium, fontStyle: Medium, fontWeight: 500, textAutoResize: WIDTH_AND_HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 2, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1190, "y": -4975.5, width: 27, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, counterAxisAlignItems: CENTER, itemSpacing: 4, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1157, "y": -4975.5, width: 60, height: 18}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: HUG, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, itemSpacing: 148, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 872, "y": -4979, width: 345, height: 25}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [{blendMode: NORMAL, type: SOLID, color: {r: 0.2235294133424759, g: 0.2235294133424759, b: 0.2235294133424759, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:23:184}}}], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, paddingLeft: 8, paddingRight: 8, paddingTop: 12, paddingBottom: 12, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5077, width: 361, height: 135}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [], cornerRadius: 8, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, itemSpacing: 16, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5268, width: 361, height: 326}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, itemSpacing: 10, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5268, width: 361, height: 326}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, itemSpacing: 20, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5614, width: 361, height: 672}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, itemSpacing: 20, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5658, width: 361, height: 362}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, itemSpacing: 378, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5658, width: 361, height: 716}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}, {id: "500:4310", name: App Bar, type: INSTANCE, boundVariables: {paddingLeft: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingTop: {type: VARIABLE_ALIAS, id: VariableID:2:45}, paddingRight: {type: VARIABLE_ALIAS, id: VariableID:2:47}, paddingBottom: {type: VARIABLE_ALIAS, id: VariableID:2:45}, fills: [{type: VARIABLE_ALIAS, id: VariableID:2:8}]}, componentId: "466:5113", componentProperties: {Property 1: {value: Default, type: VARIANT, boundVariables: {}}}, overrides: [{id: "500:4310", overriddenFields: [height, width]}], children: [{id: I500:4310;466:5114, name: Today, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:44}}, children: [{id: I500:4310;466:5115, name: Icon, type: INSTANCE, componentId: "466:4994", componentProperties: {Icon: {value: Today, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4310;466:5115;466:4995, name: Today, type: INSTANCE, componentId: "153:29", overrides: [], children: [{id: I500:4310;466:5115;466:4995;153:28, name: Vector, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:153:2}]}, absoluteBoundingBox: {x: 884.5, "y": -4876.22998046875, width: 16.000001907348633, height: 18.23000144958496}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 880.5, "y": -4879, width: 24, height: 24}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 880.5, "y": -4879, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4310;466:5116, name: Today, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:18:23}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:43}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.6549019813537598, g: 0.586928129196167, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:18:23}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 876, "y": -4855, width: 33, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Today, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: CENTER, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 876, "y": -4879, width: 33, height: 37}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4310;466:5117, name: Calendar, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:44}}, children: [{id: I500:4310;466:5118, name: Icon, type: INSTANCE, componentId: "466:4996", componentProperties: {Icon: {value: Calendar, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4310;466:5118;466:4997, name: Vector, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 962.4375, "y": -4877.3125, width: 17.625, height: 19.125}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 959.25, "y": -4879, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4310;466:5119, name: Calendar, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:43}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 949.75, "y": -4855, width: 43, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Calendar, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 949.75, "y": -4879, width: 43, height: 37}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4310;466:5120, name: Icon, type: INSTANCE, boundVariables: {rectangleCornerRadii: {RECTANGLE_TOP_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_TOP_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}, RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS: {type: VARIABLE_ALIAS, id: VariableID:2:72}}, fills: [{type: VARIABLE_ALIAS, id: VariableID:18:3}]}, componentId: "466:5038", componentProperties: {Icon: {value: Plus, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4310;466:5120;466:5039, name: plus, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:41:74}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1042.8333740234375, "y": -4867.16650390625, width: 13.333333015441895, height: 13.333333015441895}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0.5398693084716797, g: 0.4492374658584595, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:18:3}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0.5398693084716797, g: 0.4492374658584595, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:18:3}}}], strokes: [], cornerRadius: 100, cornerSmoothing: 0, strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0.5398693084716797, g: 0.4492374658584595, b: 1, a: 1}, absoluteBoundingBox: {x: 1033.5, "y": -4876.5, width: 32, height: 32}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4310;466:5121, name: Search, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:44}}, children: [{id: I500:4310;466:5122, name: Icon, type: INSTANCE, componentId: "466:4990", componentProperties: {Icon: {value: Search, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4310;466:5122;466:4991, name: Search, type: GROUP, children: [{id: I500:4310;466:5122;466:4992, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1116.25, "y": -4873.5, width: 11, height: 11}}, {id: I500:4310;466:5122;466:4993, name: Vector, type: VECTOR, boundVariables: {strokes: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, strokeJoin: ROUND, strokeCap: ROUND, absoluteBoundingBox: {x: 1125.75, "y": -4864, width: 4, height: 4}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1116.25, "y": -4873.5, width: 13.5, height: 13.5}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1110.75, "y": -4879, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4310;466:5123, name: Search, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:43}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1106.25, "y": -4855, width: 33, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Search, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: LEFT, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1106.25, "y": -4879, width: 33, height: 37}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}, {id: I500:4310;466:5124, name: Profile, type: FRAME, boundVariables: {itemSpacing: {type: VARIABLE_ALIAS, id: VariableID:2:44}}, children: [{id: I500:4310;466:5125, name: Icon, type: INSTANCE, componentId: "466:4998", componentProperties: {Icon: {value: Profile, type: VARIANT, boundVariables: {}}}, overrides: [], children: [{id: I500:4310;466:5125;466:4999, name: Profile, type: GROUP, children: [{id: I500:4310;466:5125;466:5000, name: Vector, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1192.5, "y": -4873.5, width: 8, height: 8}}, {id: I500:4310;466:5125;466:5001, name: Vector, type: VECTOR, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:35:2}]}, absoluteBoundingBox: {x: 1186.5, "y": -4877, width: 20, height: 20}}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 0.5, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1186.5, "y": -4877, width: 20, height: 20}}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], fills: [{blendMode: NORMAL, visible: false, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, absoluteBoundingBox: {x: 1184.5, "y": -4879, width: 24, height: 24}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED}, {id: I500:4310;466:5126, name: Profile, type: TEXT, boundVariables: {fills: [{type: VARIABLE_ALIAS, id: VariableID:2:42}], fontWeight: [{type: VARIABLE_ALIAS, id: VariableID:19:12}], fontSize: [{type: VARIABLE_ALIAS, id: VariableID:20:43}]}, blendMode: PASS_THROUGH, fills: [{blendMode: NORMAL, type: SOLID, color: {r: 1, g: 1, b: 1, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:42}}}], strokes: [], strokeWeight: 1, strokeAlign: OUTSIDE, absoluteBoundingBox: {x: 1180, "y": -4855, width: 33, height: 13}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG, characters: Profile, characterStyleOverrides: [], styleOverrideTable: {}, lineTypes: [NONE], lineIndentations: [0], style: {fontFamily: Geist, fontPostScriptName: Geist-Regular, fontStyle: Regular, fontWeight: 400, textAutoResize: HEIGHT, fontSize: 10, textAlignHorizontal: CENTER, textAlignVertical: TOP, letterSpacing: 0, lineHeightPx: 13, lineHeightPercent: 100, lineHeightUnit: INTRINSIC_%}, layoutVersion: 4}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: CENTER, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 1180, "y": -4879, width: 33, height: 37}, layoutAlign: INHERIT, layoutGrow: 0, layoutSizingHorizontal: FIXED, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutMode: HORIZONTAL, primaryAxisSizingMode: FIXED, counterAxisAlignItems: CENTER, primaryAxisAlignItems: SPACE_BETWEEN, paddingLeft: 12, paddingRight: 12, paddingTop: 4, paddingBottom: 4, itemSpacing: 17, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -4883, width: 361, height: 45}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: HUG}], blendMode: PASS_THROUGH, clipsContent: false, background: [], fills: [], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 0}, layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, primaryAxisAlignItems: SPACE_BETWEEN, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 864, "y": -5658, width: 361, height: 820}, layoutAlign: STRETCH, layoutGrow: 0, layoutSizingHorizontal: FILL, layoutSizingVertical: FIXED}], blendMode: PASS_THROUGH, clipsContent: true, background: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], fills: [{blendMode: NORMAL, type: SOLID, color: {r: 0, g: 0, b: 0, a: 1}, boundVariables: {color: {type: VARIABLE_ALIAS, id: VariableID:2:8}}}], strokes: [], strokeWeight: 1, strokeAlign: INSIDE, backgroundColor: {r: 0, g: 0, b: 0, a: 1}, layoutGrids: [{pattern: COLUMNS, sectionSize: 75.25, visible: true, color: {r: 1, g: 0, b: 0, a: 0.10000000149011612}, alignment: STRETCH, gutterSize: 20, offset: 16, count: 4}], layoutMode: VERTICAL, counterAxisSizingMode: FIXED, primaryAxisSizingMode: FIXED, paddingLeft: 16, paddingRight: 16, paddingTop: 32, paddingBottom: 32, layoutWrap: NO_WRAP, absoluteBoundingBox: {x: 848, "y": -5690, width: 393, height: 852}, layoutSizingHorizontal: FIXED, layoutSizingVertical: FIXED, exportSettings: [{suffix: "", format: PNG, constraint: {type: SCALE, value: 3}}]}}
