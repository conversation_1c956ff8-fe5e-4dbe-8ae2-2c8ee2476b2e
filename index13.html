<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Today Page</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #000;
      color: #fff;
    }

    .\35 00_4293 {
      position: relative;
      width: 3.93rem;
      height: 8.52rem;
      background-color: #000000;
      padding: 0.32rem 0.16rem;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .\35 00_4294 {
      position: relative;
      width: 100%;
      height: 8.20rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: transparent;
    }

    .\35 00_4295 {
      position: relative;
      width: 100%;
      height: 7.16rem;
      display: flex;
      flex-direction: column;
      gap: 3.78rem;
      background: transparent;
    }

    .\35 00_4296 {
      position: relative;
      width: 100%;
      height: 3.62rem;
      display: flex;
      flex-direction: column;
      gap: 0.20rem;
      background: transparent;
    }

    .\35 00_4297 {
      position: relative;
      width: 100%;
      height: 0.24rem;
      background-color: #000000;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 0.49rem;
      flex-wrap: wrap;
      overflow: hidden;
    }

    .I500_4297_153_543 {
      position: relative;
      width: 0.24rem;
      height: 0.24rem;
      background-image: url('/Users/<USER>/Desktop/figmaimages/I500-4297;153:543.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      flex-shrink: 0;
    }

    .I500_4297_153_545 {
      position: relative;
      flex: 1;
      font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-weight: 500;
      font-size: 0.16rem;
      line-height: 0.208rem;
      text-align: center;
      color: #ffffff;
      margin: 0;
    }

    .I500_4297_153_561 {
      position: relative;
      width: 0.24rem;
      height: 0.24rem;
      background: transparent;
      flex-shrink: 0;
    }

    .I500_4297_153_561_153_96 {
      position: relative;
      width: 0.12rem;
      height: 0.24rem;
      background-image: url('/Users/<USER>/Desktop/figmaimages/I500-4297;153:561;153:96.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }

    .I500_4301_216_1629 {
      position: relative;
      flex: 1;
      font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-weight: 500;
      font-size: 0.14rem;
      line-height: 0.182rem;
      text-align: left;
      color: #ffffff;
      margin: 0;
    }

    .I500_4301_216_1630 {
      position: relative;
      width: 0.24rem;
      height: 0.24rem;
      background-image: url('/Users/<USER>/Desktop/figmaimages/I500-4301;216:1630.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      background-color: #393939;
      border-radius: 1rem;
      flex-shrink: 0;
    }

    .to-do-list-container {
      position: relative;
      width: 100%;
      margin-bottom: 0.16rem;
    }

    .to-do-list-frame {
      position: relative;
      width: 100%;
      background-color: #000;
      border: 1px solid #393939;
      border-radius: 0.08rem;
      padding: 0.08rem;
    }

    .title-container {
      position: relative;
      width: 100%;
      height: 0.24rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.16rem;
    }

    .to-do-list-title {
      font-size: 0.14rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.182rem;
    }

    .plus-icon {
      width: 0.24rem;
      height: 0.24rem;
      background-color: #393939;
      border-radius: 1rem;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }

    .mobile-app-container {
      position: relative;
      width: 100%;
      margin-bottom: 0.16rem;
    }

    .cards {
      position: relative;
      width: 100%;
      background-color: #000;
      border: 1px solid #393939;
      border-radius: 0.08rem;
      padding: 0.12rem 0.08rem;
    }

    .card-container-1 {
      position: relative;
      width: 100%;
      margin-bottom: 0.16rem;
    }

    .title-description-icon {
      position: relative;
      width: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 0.08rem;
    }

    .title-description {
      flex: 1;
      margin-right: 0.08rem;
    }

    .mobile-app-design {
      font-size: 0.12rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.156rem;
      margin-bottom: 0.04rem;
    }

    .wireframing-colors-fonts {
      font-size: 0.10rem;
      font-weight: 400;
      color: #aaa;
      line-height: 0.13rem;
    }

    .dot-icon {
      width: 0.18rem;
      height: 0.18rem;
      background-color: #393939;
      border-radius: 1rem;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }

    .progress-bar-percentage {
      position: relative;
      width: 100%;
      margin-bottom: 0.16rem;
    }

    .progress-text {
      font-size: 0.10rem;
      font-weight: 400;
      color: #fff;
      line-height: 0.13rem;
      text-align: right;
      margin-bottom: 0.08rem;
    }

    .progress-bar {
      position: relative;
      width: 100%;
      height: 0.08rem;
      background-color: #717171;
      border-radius: 0.08rem;
    }

    .progress-fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background-color: #c6c6c6;
      border-radius: 0.08rem;
    }

    .progress-30 {
      width: 30%;
    }

    .progress-50 {
      width: 50%;
    }

    .container-2-bottom {
      position: relative;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .date-container {
      background-color: #393939;
      border-radius: 0.12rem;
      padding: 0.10rem;
    }

    .date-text {
      font-size: 0.10rem;
      font-weight: 400;
      color: #aaa;
      line-height: 0.13rem;
    }

    .icon-container {
      display: flex;
      align-items: center;
      gap: 0.04rem;
    }

    .comment-container,
    .copy-link-container {
      display: flex;
      align-items: center;
      gap: 0.02rem;
    }

    .comment-icon,
    .copy-link-icon {
      width: 0.18rem;
      height: 0.18rem;
      background-color: #393939;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }

    .comment-count,
    .copy-count {
      font-size: 0.10rem;
      font-weight: 500;
      color: #aaa;
      line-height: 0.13rem;
    }
  </style>
</head>

<body>
  <div class="500_4293">
    <div class="500_4294">
      <div class="500_4295">
        <div class="500_4296">
          <div class="500_4297">
            <div class="I500_4297_153_543"></div>
            <p class="I500_4297_153_545"></p>
            <div class="I500_4297_153_561">
              <div class="I500_4297_153_561_153_96"></div>
            </div>
          </div>
          <div class="500_4298">
            <div class="500_4299">
              <div class="500_4300">
                <div class="500_4301">
                  <p class="I500_4301_216_1629">To do List</p>
                  <div class="I500_4301_216_1630"></div>
                </div>
                <div class="500_4302">
                  <div class="500_4303">
                    <div class="I500_4303_466_5283">
                      <div class="I500_4303_466_5284">
                        <div class="I500_4303_466_5285">
                          <p class="I500_4303_466_5286"></p>
                          <p class="I500_4303_466_5287"></p>
                        </div>
                        <div class="I500_4303_466_5288"></div>
                      </div>
                      <div class="I500_4303_466_5289">
                        <p class="I500_4303_466_5290"></p>
                        <div class="I500_4303_466_5291">
                          <div class="I500_4303_466_5291_466_5252">
                            <div class="I500_4303_466_5291_466_5253"></div>
                          </div>
                          <div class="I500_4303_466_5291_466_5254">
                            <div class="I500_4303_466_5291_466_5255"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="I500_4303_466_5292">
                      <div class="I500_4303_466_5293">
                        <p class="I500_4303_466_5294"></p>
                      </div>
                      <div class="I500_4303_466_5295">
                        <div class="I500_4303_466_5296">
                          <div class="I500_4303_466_5297"></div>
                          <p class="I500_4303_466_5298"></p>
                        </div>
                        <div class="I500_4303_466_5299">
                          <div class="I500_4303_466_5300"></div>
                          <p class="I500_4303_466_5301"></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="500_4304">
                    <div class="I500_4304_466_5303">
                      <div class="I500_4304_466_5304">
                        <div class="I500_4304_466_5305">
                          <p class="I500_4304_466_5306"></p>
                          <p class="I500_4304_466_5307"></p>
                        </div>
                        <div class="I500_4304_466_5308"></div>
                      </div>
                      <div class="I500_4304_466_5309">
                        <p class="I500_4304_466_5310"></p>
                        <div class="I500_4304_466_5311">
                          <div class="I500_4304_466_5311_466_5257">
                            <div class="I500_4304_466_5311_466_5258"></div>
                          </div>
                          <div class="I500_4304_466_5311_466_5259">
                            <div class="I500_4304_466_5311_466_5260"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="I500_4304_466_5312">
                      <div class="I500_4304_466_5313">
                        <p class="I500_4304_466_5314"></p>
                      </div>
                      <div class="I500_4304_466_5315">
                        <div class="I500_4304_466_5316">
                          <div class="I500_4304_466_5317"></div>
                          <p class="I500_4304_466_5318"></p>
                        </div>
                        <div class="I500_4304_466_5319">
                          <div class="I500_4304_466_5320"></div>
                          <p class="I500_4304_466_5321"></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="500_4305">
              <div class="500_4306">
                <div class="500_4307">
                  <p class="I500_4307_216_1629"></p>
                  <div class="I500_4307_216_1630"></div>
                </div>
                <div class="500_4308">
                  <div class="I500_4308_466_5303">
                    <div class="I500_4308_466_5304">
                      <div class="I500_4308_466_5305">
                        <p class="I500_4308_466_5306"></p>
                        <p class="I500_4308_466_5307"></p>
                      </div>
                      <div class="I500_4308_466_5308"></div>
                    </div>
                    <div class="I500_4308_466_5309">
                      <p class="I500_4308_466_5310"></p>
                      <div class="I500_4308_466_5311">
                        <div class="I500_4308_466_5311_466_5257">
                          <div class="I500_4308_466_5311_466_5258"></div>
                        </div>
                        <div class="I500_4308_466_5311_466_5259">
                          <div class="I500_4308_466_5311_466_5260"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="I500_4308_466_5312">
                    <div class="I500_4308_466_5313">
                      <p class="I500_4308_466_5314"></p>
                    </div>
                    <div class="I500_4308_466_5315">
                      <div class="I500_4308_466_5316">
                        <div class="I500_4308_466_5317"></div>
                        <p class="I500_4308_466_5318"></p>
                      </div>
                      <div class="I500_4308_466_5319">
                        <div class="I500_4308_466_5320"></div>
                        <p class="I500_4308_466_5321"></p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="500_4309">
                  <div class="I500_4309_466_5303">
                    <div class="I500_4309_466_5304">
                      <div class="I500_4309_466_5305">
                        <p class="I500_4309_466_5306"></p>
                        <p class="I500_4309_466_5307"></p>
                      </div>
                      <div class="I500_4309_466_5308"></div>
                    </div>
                    <div class="I500_4309_466_5309">
                      <p class="I500_4309_466_5310"></p>
                      <div class="I500_4309_466_5311">
                        <div class="I500_4309_466_5311_466_5262">
                          <div class="I500_4309_466_5311_466_5263"></div>
                        </div>
                        <div class="I500_4309_466_5311_466_5264">
                          <div class="I500_4309_466_5311_466_5265"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="I500_4309_466_5312">
                    <div class="I500_4309_466_5313">
                      <p class="I500_4309_466_5314"></p>
                    </div>
                    <div class="I500_4309_466_5315">
                      <div class="I500_4309_466_5316">
                        <div class="I500_4309_466_5317"></div>
                        <p class="I500_4309_466_5318"></p>
                      </div>
                      <div class="I500_4309_466_5319">
                        <div class="I500_4309_466_5320"></div>
                        <p class="I500_4309_466_5321"></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="500_4310">
        <div class="I500_4310_466_5114">
          <div class="I500_4310_466_5115">
            <div class="I500_4310_466_5115_466_4995"></div>
          </div>
          <p class="I500_4310_466_5116"></p>
        </div>
        <div class="I500_4310_466_5117">
          <div class="I500_4310_466_5118"></div>
          <p class="I500_4310_466_5119"></p>
        </div>
        <div class="I500_4310_466_5120"></div>
        <div class="I500_4310_466_5121">
          <div class="I500_4310_466_5122">
            <div class="I500_4310_466_5122_466_4991"></div>
          </div>
          <p class="I500_4310_466_5123"></p>
        </div>
        <div class="I500_4310_466_5124">
          <div class="I500_4310_466_5125">
            <div class="I500_4310_466_5125_466_4999"></div>
          </div>
          <p class="I500_4310_466_5126"></p>
        </div>
      </div>
    </div>
  </div>

  <script>
    (function () {
      const designWidth = 393; // 设计稿宽度
      const baseRem = 100;      // 设定 1rem = 100px，方便换算

      function setRootFontSize () {
        const html = document.documentElement;
        const clientWidth = html.clientWidth;

        // 让页面宽度和设计稿成等比缩放
        html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
      }

      setRootFontSize();
      window.addEventListener('resize', setRootFontSize);
    })();
  </script>
</body>

</html>