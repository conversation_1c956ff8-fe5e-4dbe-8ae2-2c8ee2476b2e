你是一名资深的前端工程师，你将要根据我提供的figma信息，生成基础的dom结构，不需要有样式信息，只需要dom即可

你需要了解当前文件结构，然后开始输出一个类名给我，我会输出figma信息给你，然后

第一步：先查看/Users/<USER>/Desktop/figmaimages中，有没有名称跟id同名的图片，如果有就需要将这个图片设置为这个节点的背景图片
然后background-image是这个图片，然后background-size: cover,background-repeat: no-repeat,background-position: center;

第二步：根据提供的figma节点信息，完善样式，宽高使用absoluteBoundingBox中的width和height，单位是rem，需要自行换算

第三步：判断是否是文本节点，如果是文本节点需要设置文本内容
第三步：找到下一个需要完善样式的节点，直接输出给我，我会提供样式信息给你

直接全部完善结束

CSS类名不能以数字开头。我需要使用转义符号来处理以数字开头的类名。让我修正这个问题：