<template>
  <div class="page">
    <h1>Figma API 调试工具</h1>
    <p>输入Figma链接，选择要测试的函数</p>

    <!-- 输入区域 -->
    <div class="input-section">
      <div class="input-group">
        <label>Figma链接:</label>
        <input
          v-model="figmaUrl"
          type="url"
          placeholder="请输入Figma分享链接"
          class="url-input"
          @keyup.enter="testSelectedFunction"
        >
      </div>

      <div class="function-group">
        <label>选择测试函数:</label>
        <select v-model="selectedFunction" class="function-select">
          <option value="">请选择函数</option>
          <option value="parseFigmaUrl">parseFigmaUrl - 解析Figma链接</option>
          <option value="getFigmaInfoFromUrl">getFigmaInfoFromUrl - 获取文件信息(支持节点ID参数)</option>
          <option value="processFigmaData">processFigmaData - 处理数据(拆分)</option>
          <option value="processFigmaDataToYaml">processFigmaDataToYaml - 转换为YAML</option>
          <option value="formatFigmaToBasicDom">formatFigmaToBasicDom - 基础YAML(id,type,children)</option>
          <option value="extractDownloadableNodes">extractDownloadableNodes - 提取可下载节点</option>
          <option value="getFigmaImagesFromUrl">getFigmaImagesFromUrl - 获取图片信息</option>
          <option value="getImageNodeIdsFromUrl">getImageNodeIdsFromUrl - 获取图片节点ID</option>
          <option value="getImageUrlsFromUrl">getImageUrlsFromUrl - 获取图片URL</option>
          <option value="downloadImagesFromUrl">downloadImagesFromUrl - 下载图片</option>
        </select>
      </div>

      <!-- getFigmaInfoFromUrl 参数 -->
      <div v-if="selectedFunction === 'getFigmaInfoFromUrl'" class="params-section">
        <h4>参数设置</h4>

        <div class="param-group">
          <label>节点ID (可选):</label>
          <input
            v-model="figmaInfoParams.nodeId"
            type="text"
            placeholder="例如: 500:4293 (留空则获取文件基本信息)"
            class="param-input"
          >
          <small class="param-hint">输入节点ID可获取该节点的简化信息，删除children、components、componentSets字段</small>
        </div>
      </div>

      <!-- 图片下载参数 -->
      <div v-if="selectedFunction === 'getImageUrlsFromUrl' || selectedFunction === 'downloadImagesFromUrl'" class="params-section">
        <h4>参数设置</h4>

        <div class="param-group">
          <label>节点ID (逗号分隔):</label>
          <input
            v-model="imageParams.nodeIds"
            type="text"
            placeholder="例如: 64:192,12:21 (留空则自动获取所有图片节点)"
            class="param-input"
          >
        </div>

        <div class="param-row">
          <div class="param-group">
            <label>缩放比例:</label>
            <input
              v-model.number="imageParams.scale"
              type="number"
              min="0.5"
              max="4"
              step="0.5"
              class="param-input"
            >
          </div>

          <div class="param-group">
            <label>使用绝对边界:</label>
            <select v-model="imageParams.useAbsoluteBounds" class="param-input">
              <option :value="true">是</option>
              <option :value="false">否</option>
            </select>
          </div>
        </div>

        <div v-if="selectedFunction === 'downloadImagesFromUrl'" class="param-group">
          <label>下载路径:</label>
          <input
            v-model="imageParams.downloadPath"
            type="text"
            placeholder="例如: /Users/<USER>/Downloads/figma-images"
            class="param-input"
          >
        </div>
      </div>

      <button
        @click="testSelectedFunction"
        :disabled="!figmaUrl || !selectedFunction || loading"
        class="test-btn"
      >
        {{ loading ? '测试中...' : '开始测试' }}
      </button>
    </div>

    <!-- 结果显示区域 -->
    <div class="result-section" v-if="result || error">
      <div class="result-header">
        <h3>测试结果</h3>
        <button @click="clearResult" class="clear-btn">清空</button>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-box">
        <h4>❌ 错误信息:</h4>
        <pre>{{ error }}</pre>
      </div>

      <!-- 成功结果 -->
      <div v-if="result" class="result-box">
        <div class="result-title">
          <h4>✅ 执行结果:</h4>
          <button @click="copyResult" class="copy-btn" :disabled="copying" :class="{ 'copy-success': copySuccess }">
            {{ copying ? '复制中...' : copySuccess ? '已复制!' : '复制结果' }}
          </button>
        </div>
        <div class="result-info">
          <p><strong>函数:</strong> {{ selectedFunction }}</p>
          <p><strong>执行时间:</strong> {{ executionTime }}ms</p>
          <p><strong>数据类型:</strong> {{ typeof result }}</p>
          <p v-if="Array.isArray(result)"><strong>数组长度:</strong> {{ result.length }}</p>
          <p v-else-if="typeof result === 'object' && result"><strong>对象键数:</strong> {{ Object.keys(result).length }}</p>
        </div>
        <div class="result-content">
          <pre ref="resultPre">{{ formatResult(result) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  parseFigmaUrl,
  getFigmaInfoFromUrl,
  processFigmaData,
  processFigmaDataToYaml,
  formatFigmaToBasicDom,
  extractDownloadableNodes,
  getFigmaImagesFromUrl,
  getImageNodeIdsFromUrl,
  getImageUrlsFromUrl,
  downloadImages
} from '../api/figma'

// 响应式数据
const figmaUrl = ref('https://www.figma.com/design/w7ZjgUryNqGb1UPKXCOoO6/Task-Management--Todo-App--Community-?node-id=500-4293&t=0cP6qb0lWwh00jvz-4')
const selectedFunction = ref('')
const loading = ref(false)
const result = ref<any>(null)
const error = ref('')
const executionTime = ref(0)
const copying = ref(false)
const copySuccess = ref(false)
const resultPre = ref<HTMLPreElement | null>(null)

// getFigmaInfoFromUrl 参数
const figmaInfoParams = ref({
  nodeId: ''
})

// 图片下载参数
const imageParams = ref({
  nodeIds: '',
  scale: 2,
  useAbsoluteBounds: true,
  downloadPath: '/Users/<USER>/figma-images'
})

// 测试选中的函数
const testSelectedFunction = async () => {
  if (!figmaUrl.value || !selectedFunction.value) return

  loading.value = true
  error.value = ''
  result.value = null

  const startTime = Date.now()

  try {
    console.log(`🧪 开始测试函数: ${selectedFunction.value}`)
    console.log(`🔗 Figma链接: ${figmaUrl.value}`)

    let testResult: any

    switch (selectedFunction.value) {
      case 'parseFigmaUrl':
        testResult = await parseFigmaUrl(figmaUrl.value)
        break

      case 'getFigmaInfoFromUrl':
        const nodeId = figmaInfoParams.value.nodeId.trim() || undefined
        testResult = await getFigmaInfoFromUrl(figmaUrl.value, nodeId)
        break

      case 'processFigmaData':
        const figmaInfo1 = await getFigmaInfoFromUrl(figmaUrl.value)
        testResult = processFigmaData(figmaInfo1)
        break

      case 'processFigmaDataToYaml':
        const figmaInfo2 = await getFigmaInfoFromUrl(figmaUrl.value)
        testResult = processFigmaDataToYaml(figmaInfo2)
        break

      case 'formatFigmaToBasicDom':
        const figmaInfo3 = await getFigmaInfoFromUrl(figmaUrl.value)
        testResult = formatFigmaToBasicDom(figmaInfo3)
        break

      case 'extractDownloadableNodes':
        const figmaInfo4 = await getFigmaInfoFromUrl(figmaUrl.value)
        testResult = extractDownloadableNodes(figmaInfo4)
        break

      case 'getFigmaImagesFromUrl':
        testResult = await getFigmaImagesFromUrl(figmaUrl.value)
        break

      case 'getImageNodeIdsFromUrl':
        testResult = await getImageNodeIdsFromUrl(figmaUrl.value)
        break

      case 'getImageUrlsFromUrl':
        // 如果没有指定节点ID，先获取所有图片节点ID
        let nodeIds = imageParams.value.nodeIds.trim()
        if (!nodeIds) {
          console.log('🔍 未指定节点ID，正在获取所有图片节点ID...')
          nodeIds = await getImageNodeIdsFromUrl(figmaUrl.value)
          console.log(`📋 获取到的节点ID: ${nodeIds}`)
        }

        testResult = await getImageUrlsFromUrl(
          figmaUrl.value,
          nodeIds,
          imageParams.value.scale,
          imageParams.value.useAbsoluteBounds
        )
        break

      case 'downloadImagesFromUrl':
        // 如果没有指定节点ID，先获取所有图片节点ID
        let downloadNodeIds = imageParams.value.nodeIds.trim()
        if (!downloadNodeIds) {
          console.log('🔍 未指定节点ID，正在获取所有图片节点ID...')
          downloadNodeIds = await getImageNodeIdsFromUrl(figmaUrl.value)
          console.log(`📋 获取到的节点ID: ${downloadNodeIds}`)
        }

        // 获取图片URL
        console.log('🌐 正在获取图片URL...')
        const imageUrls = await getImageUrlsFromUrl(
          figmaUrl.value,
          downloadNodeIds,
          imageParams.value.scale,
          imageParams.value.useAbsoluteBounds
        )
        console.log(`📸 获取到 ${Object.keys(imageUrls).length} 个图片URL`)

        // 下载图片
        console.log(`💾 开始下载图片到: ${imageParams.value.downloadPath}`)
        const filePaths = await downloadImages(imageUrls, imageParams.value.downloadPath)

        testResult = {
          imageUrls,
          downloadedFiles: filePaths,
          summary: {
            totalImages: Object.keys(imageUrls).length,
            downloadedCount: filePaths.length,
            downloadPath: imageParams.value.downloadPath
          }
        }
        break

      default:
        throw new Error('未知的函数')
    }

    executionTime.value = Date.now() - startTime
    result.value = testResult

    console.log(`✅ 测试完成，耗时: ${executionTime.value}ms`)
    console.log('📊 结果:', testResult)

  } catch (err) {
    executionTime.value = Date.now() - startTime
    error.value = err instanceof Error ? err.message : '未知错误'
    console.error('❌ 测试失败:', err)
  } finally {
    loading.value = false
  }
}

// 格式化结果显示
const formatResult = (data: any): string => {
  if (typeof data === 'string') {
    return data
  }
  return JSON.stringify(data, null, 2)
}

// 复制结果到剪贴板
const copyResult = async () => {
  if (!result.value) return

  copying.value = true

  try {
    const textToCopy = formatResult(result.value)

    // 尝试使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(textToCopy)
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = textToCopy
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    console.log('✅ 结果已复制到剪贴板')
    copying.value = false
    copySuccess.value = true

    // 2秒后重置成功状态
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)

  } catch (err) {
    console.error('❌ 复制失败:', err)
    copying.value = false
  }
}

// 清空结果
const clearResult = () => {
  result.value = null
  error.value = ''
  executionTime.value = 0
  copySuccess.value = false
  copying.value = false
}
</script>

<style scoped>
.page {
  padding: 24px;
  color: white;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 8px;
  font-size: 28px;
  color: #fff;
}

p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 32px;
}

/* 输入区域样式 */
.input-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group, .function-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #fff;
}

.url-input, .function-select {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.url-input:focus, .function-select:focus {
  border-color: #007bff;
}

.url-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.function-select option {
  background: #2d2d2d;
  color: white;
}

.test-btn {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 15px;
}

.test-btn:hover:not(:disabled) {
  background: #0056b3;
}

.test-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* 参数设置区域样式 */
.params-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.params-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #fff;
  font-weight: 500;
}

.param-group {
  margin-bottom: 16px;
}

.param-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.param-input {
  width: 100%;
  padding: 10px 14px;
  font-size: 14px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.2);
  color: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.param-input:focus {
  border-color: #007bff;
}

.param-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.param-input option {
  background: #2d2d2d;
  color: white;
}

.param-hint {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* 结果区域样式 */
.result-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  color: #fff;
}

.clear-btn {
  padding: 6px 12px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.error-box, .result-box {
  padding: 24px;
}

.error-box h4, .result-box h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.error-box h4 {
  color: #ff6b6b;
}

.result-box h4 {
  color: #51cf66;
}

.result-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-title h4 {
  margin: 0;
}

.copy-btn {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.copy-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.copy-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  transform: none;
}

.copy-btn.copy-success {
  background: #51cf66;
  color: white;
}

.copy-btn.copy-success:hover {
  background: #40c057;
}

.result-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-info p {
  margin: 4px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.result-content {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 500px;
  overflow-y: auto;
}

pre {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #fff;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 滚动条样式 */
.result-content::-webkit-scrollbar {
  width: 8px;
}

.result-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.result-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.result-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
