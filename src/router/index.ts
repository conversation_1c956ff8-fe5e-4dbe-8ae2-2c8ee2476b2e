import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件
import Dashboard from '../views/Dashboard.vue'
import GenerateUI from '../views/generateUI.vue'
import Interface from '../views/interface.vue'
import debug from '@/views/debug.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/generateUI',
    name: 'GenerateUI',
    component: GenerateUI
  },
    {
    path: '/debug',
    name: 'debug',
    component: debug
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
