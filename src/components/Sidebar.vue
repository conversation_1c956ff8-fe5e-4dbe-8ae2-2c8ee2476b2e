<template>
  <div class="sidebar">
    <!-- Logo区域 -->
    <div class="logo-section">
      <div class="logo">
        <div class="logo-icon">C</div>
        <span class="logo-text">ClarityUI</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <router-link v-for="item in menuItems" :key="item.path" :to="item.path" class="nav-item" :class="{ 'has-children': item.children }">
        <div class="nav-item-content">
          <i :class="item.icon" class="nav-icon"></i>
          <span class="nav-text">{{ item.name }}</span>
          <i v-if="item.children" class="chevron-icon">▼</i>
        </div>
      </router-link>
    </nav>

    <!-- 用户信息 -->
    <div class="user-section">
      <div class="user-info">
        <div class="user-avatar">
          <div class="avatar-placeholder">MJ</div>
        </div>
        <div class="user-details">
          <div class="user-name"><PERSON><PERSON></div>
        </div>
        <i class="settings-icon">⚙</i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface MenuItem {
  name: string;
  path: string;
  icon: string;
  children?: MenuItem[];
}

const menuItems = ref<MenuItem[]>([
  {
    name: "Dashboard",
    path: "/dashboard",
    icon: "📊",
  },
  {
    name: "UI生成",
    path: "/generateUI",
    icon: "🎫",
  },
  {
    name: "交互设计",
    path: "/interface",
    icon: "👥",
  },
  {
    name: "Customers",
    path: "/customers",
    icon: "👤",
  },
  {
    name: "Products",
    path: "/products",
    icon: "📦",
  },
  {
    name: "Orders",
    path: "/orders",
    icon: "📋",
  },
  {
    name: "Analytics",
    path: "/analytics",
    icon: "📈",
  },
  {
    name: "Settings",
    path: "/settings",
    icon: "⚙️",
  },
  {
    name: "Debug",
    path: "/debug",
    icon: "⚙️",
  },
]);
</script>

<style scoped>
.sidebar {
  width: 280px;
  background: #1e1e1e;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  border-right: 1px solid #333;
}

.logo-section {
  padding: 24px 20px;
  border-bottom: 1px solid #333;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: #4f46e5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.logo-text {
  color: #f8fafc;
  font-size: 18px;
  font-weight: 600;
}

.nav-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-item {
  display: block;
  text-decoration: none;
  color: #94a3b8;
  margin: 2px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: #2d2d2d;
  color: #f8fafc;
}

.nav-item.router-link-active {
  background: #2d2d2d;
  color: white;
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.nav-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.nav-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.chevron-icon {
  font-size: 10px;
  opacity: 0.6;
}

.user-section {
  padding: 20px;
  border-top: 1px solid #333;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #f8fafc;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
}

.settings-icon {
  font-size: 16px;
  opacity: 0.7;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.settings-icon:hover {
  opacity: 1;
}
</style>
